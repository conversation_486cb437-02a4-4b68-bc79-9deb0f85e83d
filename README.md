# admin-front 精简说明

本分支已按“生成环境简化”要求移除以下功能：

- 暗黑模式：移除切换按钮与相关运行时逻辑，固定为亮色模式。
- 主题自定义：移除颜色主题选择与构建期主题切换插件。

关键变更点：

- `src/settings/projectSetting.ts`：`showDarkModeToggle` 设为 `false`。
- `src/layouts/default/setting/SettingDrawer.tsx`：移除暗黑开关与主题颜色选择 UI。
- `src/layouts/default/setting/handler.ts`：忽略 `CHANGE_THEME` 与 `CHANGE_THEME_COLOR` 事件。
- `src/views/sys/login/Login.vue`、`src/views/system/loginmini/MiniLogin.vue`：移除 `AppDarkModeToggle` 引用。
- `src/components/Application/src/AppDarkModeToggle.vue`：留空实现以保持兼容。
- `src/logics/initAppConfig.ts`：不再初始化暗黑模式，固定亮色逻辑。
- `src/logics/theme/dark.ts`：固定写入 `data-theme='light'`。
- `src/logics/theme/updateBackground.ts`：不再根据暗黑模式分支，统一按亮色处理。
- `build/vite/plugin/index.ts`：停止注册主题相关插件。
- `build/vite/plugin/theme.ts`：去除 `antdDarkThemePlugin`，保留基础变量替换能力（如需）。

注意事项：

- 如需恢复暗黑或主题自定义，请参考以上文件反向启用相应代码。
- 其余 UI 与业务功能不受影响。


## Nuxt 4 改造说明

本项目已支持以 Nuxt 4 方式运行（默认 SPA 模式，禁用文件式路由，沿用自定义路由与现有目录结构）。

- 入口与模板：新增 `app/app.html`，复用了原 `index.html` 的全局样式与脚本（如 ActiveReports、身份证读卡与 Lodop）。
- 配置文件：新增 `nuxt.config.ts`，设置 `ssr: false`、`pages: false`，并复用 `vite` 配置（less 变量、svg 雪碧图）。
- 自定义路由：新增 `app/router.options.ts`，让 Nuxt 使用 `src/router/routes` 中定义的路由表。
- 启动插件：新增 `app/plugins/register-app.client.ts`，在 Nuxt 容器中初始化 Pinia、i18n、指令、错误处理与路由守卫，并延后注册第三方组件。

运行方式：

1. 安装依赖：`pnpm i` 或 `npm i`
2. 开发启动（Nuxt）：`npx nuxt dev`
3. 生产构建：`npx nuxt build && npx nuxt preview`

注意：

- 如需开启 SSR，可在 `nuxt.config.ts` 中将 `ssr` 设为 `true` 并评估与现有浏览器 API 的兼容性。
- 如需启用文件式路由，可移除 `pages: false` 并在 `app/pages` 下新增页面；当前默认沿用自定义路由。

