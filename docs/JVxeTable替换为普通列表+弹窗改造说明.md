# JVxeTable 替换为普通列表+弹窗改造说明

## 概述

本文档记录了将 `ReportSettingGroupForm.vue` 中的 JVxeTable 组件替换为普通 Ant Design Vue 表格 + 弹窗编辑模式的详细改造过程。

## 改造背景

JVxeTable 是一个复杂的表格组件，具有内联编辑功能，但在某些场景下可能存在以下问题：
- 组件复杂度高，维护成本大
- 内联编辑体验可能不够友好
- 与标准 Ant Design Vue 组件库的一致性问题

因此决定将其替换为更简洁的普通表格 + 弹窗编辑模式。

## 改造前后对比

### 改造前（JVxeTable）

```vue
<JVxeTable
  keep-source
  resizable
  ref="reportSettingDepart"
  v-if="reportSettingDepartTable.show"
  :loading="reportSettingDepartTable.loading"
  :columns="reportSettingDepartTable.columns"
  :dataSource="reportSettingDepartTable.dataSource"
  :height="340"
  :disabled="formDisabled"
  :rowNumber="true"
  :rowSelection="true"
  :toolbar="true"
/>
```

### 改造后（普通表格+弹窗）

```vue
<div class="mb-2">
  <a-button type="dashed" @click="addDepart" :disabled="formDisabled">新增科室</a-button>
</div>
<a-table
  :data-source="reportSettingDepartTable.dataSource"
  :columns="departColumns"
  :pagination="false"
  :rowKey="row => row.id || row._key"
  :loading="reportSettingDepartTable.loading"
  :scroll="{ y: 340, x: 800 }"
  size="small"
>
  <template #bodyCell="{ column, record, index }">
    <template v-if="column.dataIndex === 'departmentId'">
      <span>{{ renderDictText('sys_depart,depart_name,id', record.departmentId) }}</span>
    </template>
    <template v-else-if="column.key === 'action'">
      <a-space>
        <a-button type="link" @click="editDepart(record, index)" :disabled="formDisabled">编辑</a-button>
        <a-button type="link" danger @click="removeDepart(record, index)" :disabled="formDisabled">删除</a-button>
      </a-space>
    </template>
  </template>
</a-table>
<BasicModal @register="registerDepartModal" :title="departModalTitle" :width="640" @ok="handleDepartOk">
  <BasicForm @register="registerDepartForm" />
</BasicModal>
```

## 详细改造步骤

### 1. 模板部分改造

#### 1.1 替换科室关联表格

- 移除 `JVxeTable` 组件
- 添加新增按钮
- 使用 `a-table` 组件替换
- 添加操作列（编辑、删除按钮）
- 添加编辑弹窗 `BasicModal`

#### 1.2 替换大项关联表格

- 同样的改造模式
- 针对大项数据的特定配置

### 2. 脚本部分改造

#### 2.1 导入更新

**移除：**
```typescript
import { JVxeTable } from '/@/components/jeecg/JVxeTable';
import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
```

**新增：**
```typescript
import { BasicModal, useModal } from '/@/components/Modal';
import { getDictItemsByCode } from '/@/utils/dict/index';
```

#### 2.2 数据结构调整

**移除列配置依赖：**
```typescript
// 移除对 reportSettingDepartColumns, reportSettingItemgroupColumns 的依赖
```

**新增列配置：**
```typescript
const departColumns = [
  { title: '科室', dataIndex: 'departmentId', width: 300 },
  { title: '排序', dataIndex: 'seq', width: 200 },
  { title: '操作', key: 'action', width: 150, fixed: 'right' },
];
```

#### 2.3 方法重构

**替换 useJvxeMethod：**
```typescript
// 移除
const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(/*...*/);

// 替换为
const formRef = ref();
function handleChangeTabs() {}
async function requestSubTableData(api, params, table, done) {
  // 自定义实现
}
```

**新增弹窗相关方法：**
```typescript
// 科室弹窗
const [registerDepartModal, { openModal: openDepartModal, closeModal: closeDepartModal }] = useModal();
const [registerDepartForm, { setFieldsValue: setDepartFieldsValue, resetFields: resetDepartFields, validate: validateDepartForm }] = useForm({
  // 表单配置
});

// 大项弹窗
const [registerItemgroupModal, { openModal: openItemgroupModal, closeModal: closeItemgroupModal }] = useModal();
const [registerItemgroupForm, { setFieldsValue: setItemgroupFieldsValue, resetFields: resetItemgroupFields, validate: validateItemgroupForm }] = useForm({
  // 表单配置
});
```

#### 2.4 CRUD 操作实现

**新增操作：**
```typescript
function addDepart() {
  editingDepartIndex = -1;
  resetDepartFields();
  departModalTitle.value = '新增科室';
  openDepartModal(true, {});
}
```

**编辑操作：**
```typescript
function editDepart(record, index) {
  editingDepartIndex = index;
  resetDepartFields();
  setDepartFieldsValue({ ...record });
  departModalTitle.value = '编辑科室';
  openDepartModal(true, {});
}
```

**删除操作：**
```typescript
function removeDepart(_row, index) {
  reportSettingDepartTable.dataSource.splice(index, 1);
}
```

**保存操作：**
```typescript
async function handleDepartOk() {
  const values = await validateDepartForm();
  if (editingDepartIndex >= 0) {
    reportSettingDepartTable.dataSource.splice(editingDepartIndex, 1, { ...reportSettingDepartTable.dataSource[editingDepartIndex], ...values });
  } else {
    reportSettingDepartTable.dataSource.push({ ...values, _key: `temp_${Date.now()}` });
  }
  closeDepartModal();
}
```

### 3. 数据处理逻辑调整

#### 3.1 数据提交格式化

```typescript
function classifyIntoFormData(formValue) {
  return {
    ...formValue,
    reportSettingDepartList: reportSettingDepartTable.dataSource.map((row, idx) => ({
      id: row.id,
      departmentId: row.departmentId,
      seq: row.seq ?? idx + 1,
    })),
    reportSettingItemgroupList: reportSettingItemgroupTable.dataSource.map((row, idx) => ({
      id: row.id,
      itemgroupId: row.itemgroupId,
      seq: row.seq ?? idx + 1,
    })),
  };
}
```

#### 3.2 字典数据渲染

```typescript
function renderDictText(dictCode: string, value: any) {
  const arr = getDictItemsByCode(dictCode) || [];
  const hit = arr.find((it) => String(it.value) === String(value));
  return hit ? hit.text : value;
}
```

## 改造优势

### 1. 代码简化
- 移除了复杂的 JVxeTable 依赖
- 使用标准的 Ant Design Vue 组件
- 代码结构更清晰，易于维护

### 2. 用户体验提升
- 弹窗编辑模式提供更好的编辑体验
- 操作更直观，减少误操作
- 表单验证更友好

### 3. 技术栈统一
- 完全使用 Ant Design Vue 组件体系
- 减少第三方组件依赖
- 提高代码一致性

## 注意事项

1. **数据格式兼容性**：确保新的数据处理逻辑与后端 API 兼容
2. **表单验证**：在弹窗表单中添加适当的验证规则
3. **用户权限**：保持原有的权限控制逻辑
4. **数据回显**：确保编辑时数据正确回显到弹窗表单中

## 测试建议

1. **功能测试**：验证新增、编辑、删除功能正常
2. **数据验证**：确保表单验证规则生效
3. **权限测试**：验证在不同权限下的表现
4. **兼容性测试**：确保与现有业务流程兼容

## 总结

通过将 JVxeTable 替换为普通表格+弹窗模式，实现了：
- 代码简化和维护性提升
- 用户体验的改善
- 技术栈的统一

这种改造模式可以作为其他类似组件改造的参考模板。
