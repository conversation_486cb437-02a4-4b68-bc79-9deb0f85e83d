import { defineNuxtConfig } from 'nuxt/config'
import { resolve } from 'node:path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import UnoCSS from 'unocss/vite'
import { presetUno, presetTypography } from 'unocss'
import { generateModifyVars } from './build/generate/generateModifyVars'

export default defineNuxtConfig({
  future: {
    compatibilityVersion: 4,
  },
  // 先以 SPA 方式运行，后续可按需开启 SSR
  ssr: false,
  // 禁用 file-based pages，复用现有自定义路由
  pages: false,
  // 保持现有目录结构与别名
  alias: {
    '/@/': resolve('./src') + '/',
    '/#/': resolve('./types') + '/',
    '@/': resolve('./src') + '/',
    '#/': resolve('./types') + '/',
  },
  // 静态资源目录沿用项目的 public/
  dir: {
    public: 'public',
  },
  nitro: {
    prerender: {
      // 如有需要可在此添加需要预渲染的路由
      routes: [],
    },
  },
  vite: {
    define: {
      'import.meta.env.VITE_PUBLIC_PATH': JSON.stringify('/'),
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          // 复用原有 antd 主题变量
          modifyVars: generateModifyVars(),
        },
      },
    },
    plugins: [
      UnoCSS({ presets: [presetUno(), presetTypography()] }) as any,
      // 与原 Vite 配置保持一致的 svg 雪碧图插件
      createSvgIconsPlugin({
        iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
        symbolId: 'icon-[dir]-[name]'
      }) as any,
    ],
  },
  modules: [
    // 如需启用 UnoCSS，可开启下行模块并移除 main.ts 中的 uno.css 直接引入
    // '@unocss/nuxt',
  ],
})


