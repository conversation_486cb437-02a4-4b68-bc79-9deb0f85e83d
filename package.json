{"name": "jeecgboot-vue3", "version": "3.7.1", "author": {"name": "呼和浩特市邦健信息技术有限", "email": "<EMAIL>", "url": "https://www.yingyangyun.cn"}, "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "nuxt dev --port 5173", "build": "nuxt build", "preview": "nuxt preview --port 5173", "vite:dev": "vite", "vite:build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:report": "pnpm clean:cache && cross-env REPORT=true npm run vite:build", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install"}, "dependencies": {"@ant-design/colors": "^7.2.0", "@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "4.0.7", "@traptitech/markdown-it-katex": "^3.6.0", "@types/localforage": "^0.0.34", "@vant/area-data": "^1.5.2", "@vue/shared": "^3.5.13", "@vueuse/core": "^10.11.1", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.1.1", "axios": "^1.7.9", "bpmn-js": "^17.11.1", "china-area-data": "^5.0.1", "cleave.js": "^1.6.0", "clipboard": "^2.0.11", "codemirror": "^5.65.18", "cron-parser": "^4.9.0", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "deviceorientation-js": "^1.0.0", "dom-align": "^1.12.4", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "enquire.js": "^2.1.6", "event-source-polyfill": "^1.0.31", "highlight.js": "^11.11.1", "id-validator": "^1.3.0", "intro.js": "^7.2.0", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "lodash.get": "^4.4.2", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "mathjs": "^8.1.1", "md5": "^2.3.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.3.0", "pinia": "3.0.3", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "resize-observer-polyfill": "^1.5.1", "rotate-canvas": "^1.0.0", "segmentit": "^2.0.3", "showdown": "^2.1.0", "sm-crypto": "^0.3.13", "socket.io-client": "^4.8.1", "sortablejs": "^1.15.6", "splitpanes": "^3.1.8", "tinymce": "6.6.2", "uuid": "^11.0.2", "vditor": "^3.10.8", "vue": "^3.5.18", "vue-cropper": "^0.6.5", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.14.2", "vue-infinite-scroll": "^2.0.2", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.5.1", "vue-types": "^5.1.3", "vue3-image-preview": "^0.2.7", "vuedraggable": "^4.1.0", "xss": "^1.0.15"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@iconify/json": "^2.2.215", "@purge-icons/generated": "^0.10.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.2.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/intro.js": "^5.1.5", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/marked": "^6.0.0", "@types/mockjs": "^1.0.10", "@types/node": "^20.12.13", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/showdown": "^2.0.6", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@unocss/nuxt": "^0.58.9", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "cz-git": "^1.9.2", "czg": "^1.9.2", "dompurify": "^3.2.5", "dotenv": "^16.4.5", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "esno": "^4.8.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "husky": "^8.0.3", "inquirer": "^9.2.22", "is-ci": "^3.0.1", "jest": "^29.7.0", "less": "^4.2.0", "lint-staged": "15.2.2", "marked": "^15.0.9", "npm-run-all": "^4.1.5", "nuxt": "^4.0.3", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "prettier": "^3.4.2", "pretty-quick": "^4.0.0", "rimraf": "^5.0.7", "rollup": "^4.18.0", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^16.10.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "typescript": "^4.9.5", "unocss": "^0.58.9", "vite": "^6.0.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.5", "vite-plugin-mock": "^2.9.8", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^1.8.27"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/JeecgBoot.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/JeecgBoot/issues"}, "homepage": "https://www.jeecg.com", "engines": {"node": "^18 || >=20"}}