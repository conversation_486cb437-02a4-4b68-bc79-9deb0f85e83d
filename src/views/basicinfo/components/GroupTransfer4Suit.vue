<template>
  <div style="height: 75vh">
    <a-alert v-if="!props.suitId" message="请先保存基本信息" type="error" :show-icon="true" :closable="false" style="margin-bottom: 10px" />
    <a-row :gutter="16">
      <a-col :span="10">
        <a-card size="small" title="待选组合" style="padding: 5px; height: 75vh">
          <a-row :gutter="8">
            <a-col :span="8">
              <div style="max-height: 65vh; overflow-y: scroll">
                <depart-tree @select="searchGroupByDepartment" />
              </div>
            </a-col>
            <a-col :span="16">
              <a-form layout="inline" style="margin-bottom: 10px">
                <a-form-item labelAlign="left">
                  <a-input-search allow-clear @change="searchGroupByKeyword" size="middle" placeholder="名称或助记码查询" />
                </a-form-item>
              </a-form>
              <a-table
                :loading="groupLoading"
                :bordered="false"
                :scroll="{ y: '60vh' }"
                :pagination="false"
                row-key="id"
                :columns="groupColumns"
                :data-source="groupDatasource"
                :custom-row="customRow"
                size="small"
                :row-selection="{ selectedRowKeys: groupTableState.selectedRowKeys, onChange: onGroupTableSelectChange }"
              >
                <template #bodyCell="{ text, record, index, column }">
                  <template v-if="column.dataIndex === 'operation'">
                    <a @click.stop="showItem(record)">项目</a>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      <a-col :span="1" style="display: flex; flex-direction: column; justify-content: center; align-items: center">
        <a-space direction="vertical" size="middle">
          <a-button size="small" type="primary" :disabled="groupTableState.selectedRowKeys.length == 0" @click="handleAdd">
            <RightOutlined />加
          </a-button>
          <a-button size="small" type="primary" :disabled="suitTableState.selectedRowKeys.length == 0" @click="handleRemove">
            <LeftOutlined />减
          </a-button>
        </a-space>
      </a-col>
      <a-col :span="13">
        <a-card size="small" title="已选组合" style="padding: 5px; height: 75vh">
          <template #extra
            ><span>总价：</span><span style="color: #f5222d; font-weight: bold; font-size: 16px">{{ totalItemPrice.toFixed(2) }}</span
            ><span style="margin-left: 20px">折后总价：</span
            ><span style="color: #f5222d; font-weight: bold; font-size: 16px">{{ totalPriceAfterDis.toFixed(2) }}</span></template
          >
          <!-- 依赖项目提示区域 -->
          <div v-if="missingDependencies.length > 0" class="missing-dependencies-alert" style="margin-bottom: 16px">
            <a-alert type="warning" show-icon :closable="false">
              <template #message>
                <div class="missing-dependencies-content">
                  <span class="alert-title">检测到缺失的依赖项目</span>
                  <div class="missing-projects-list">
                    <a-tag
                      v-for="dependency in missingDependencies"
                      :key="dependency.dependentId"
                      color="orange"
                      style="margin: 2px 4px 2px 0"
                      :title="`依赖此项目的检查项目: ${dependency.relatedItemsText}`"
                    >
                      {{ dependency.dependentName }}
                      <template v-if="dependency.dependentItemDetails"> ({{ dependency.dependentItemDetails }}) </template>
                    </a-tag>
                  </div>
                </div>
              </template>
              <template #action>
                <a-space>
                  <a-button
                    type="primary"
                    size="small"
                    :loading="addingDependencies"
                    @click="handleQuickAddAllDependencies"
                  >
                    一键添加
                  </a-button>
                  <a-button
                    size="small"
                    @click="openDependencyQuickAddModal"
                  >
                    选择添加
                  </a-button>
                </a-space>
              </template>
            </a-alert>
          </div>

          <a-form layout="inline" :model="batchFormState" style="margin-bottom: 10px">
            <a-form-item labelAlign="left" tooltip="折扣率与折后总价只能设置一个，同时设置时以折扣率为准">
              <a-input-number v-model:value="batchFormState.disRate" size="small" placeholder="折扣率" />
            </a-form-item>
            <a-form-item labelAlign="left">
              <a-input-number v-model:value="batchFormState.priceAfterDis" size="small" placeholder="折后总价" />
            </a-form-item>
            <a-form-item labelAlign="left">
              <a-select
                size="small"
                v-model:value="batchFormState.type"
                placeholder="项目类型"
                style="width: 100px"
                :options="[
                  { label: '健康项目', value: '健康项目' },
                  { label: '职业项目', value: '职业项目' },
                ]"
              />
            </a-form-item>
            <a-form-item>
              <a-popconfirm :title="batchTip" ok-text="确定" cancel-text="取消" @confirm="updateByBatch">
                <a-button type="primary" size="small" html-type="submit">整体设置</a-button>
              </a-popconfirm>
            </a-form-item>
          </a-form>
          <a-table
            :loading="suitLoading"
            :bordered="false"
            :scroll="{ y: '60vh' }"
            :pagination="false"
            row-key="uuid"
            :columns="suitColumn"
            :data-source="suitDataSource"
            :row-selection="{ selectedRowKeys: suitTableState.selectedRowKeys, onChange: onSuitTableSelectChange }"
            size="small"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="'disRate' == column.dataIndex">
                <input
                  type="number"
                  min="0"
                  @change="handleDisRateChange(record, $event)"
                  :value="record.disRate"
                  style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 80px"
                />
              </template>
              <template v-else-if="'priceAfterDis' == column.dataIndex">
                <input
                  type="number"
                  min="0"
                  @change="handlePriceChange(record, $event)"
                  :value="record.priceAfterDis"
                  style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 80px"
                />
              </template>
              <template v-else-if="'minDiscountRate' == column.dataIndex">
                <input
                  v-if="hasPermission('suit:minDiscountRate')"
                  type="number"
                  min="0"
                  step="0.1"
                  @change="updateMinDiscountRate(record, $event)"
                  :value="record.minDiscountRate"
                  style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
                />
                <span v-else>
                  {{ record.minDiscountRate }}
                </span>
              </template>
              <template v-else-if="column.dataIndex == 'type'">
                <div>
                  <a-select
                    @change="updateType(record)"
                    size="small"
                    v-model:value="record.type"
                    style="width: 100px"
                    :options="[
                      { label: '健康项目', value: '健康项目' },
                      { label: '职业项目', value: '职业项目' },
                    ]"
                  />
                </div>
              </template>
              <template v-else>
                {{ text }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>

    <group-item-modal ref="groupItemModal" />

    <!-- 依赖项目快捷添加模态框 -->
    <DependencyQuickAddModal
      ref="dependencyQuickAddModalRef"
      @quick-add="handleDependencyQuickAdd"
      @confirm="handleDependencyConfirm"
      @cancel="handleDependencyCancel"
    />

    <!-- 批量部位选择模态框 -->
    <a-modal
      :title="batchPartState.itemGroups.length === 1 ? '选择检查部位' : '批量选择检查部位'"
      v-model:open="batchPartState.visible"
      width="800px"
      @ok="confirmBatchAddItemsWithParts"
      @cancel="closeBatchPartSelector"
      :confirmLoading="batchPartState.loading"
      :okButtonProps="{ disabled: !hasAnyPartSelected() }"
      okText="确认添加"
      cancelText="取消"
    >
      <div style="padding: 10px; height: 60vh; overflow-y: auto;">
        <div style="margin-bottom: 16px;">
          <a-alert
            :message="`共 ${batchPartState.itemGroups.length} 个项目需要选择部位`"
            type="info"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <div
          v-for="itemGroup in batchPartState.itemGroups"
          :key="itemGroup.id"
          style="margin-bottom: 24px; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px; background-color: #fafafa;"
        >
          <div style="margin-bottom: 12px;">
            <a-tag color="blue" style="margin-bottom: 8px;">{{ itemGroup.name }}</a-tag>
            <div style="font-size: 12px; color: #666;">
              科室：{{ itemGroup.departmentName }} | 价格：¥{{ itemGroup.price }}
            </div>
          </div>

          <a-form-item :label="`选择部位`" required style="margin-bottom: 8px;">
            <a-select
              v-model:value="getItemPartSelection(itemGroup.id).selectedParts"
              mode="multiple"
              placeholder="请选择检查部位，支持多选，可输入关键字搜索（支持拼音缩写）"
              :filter-option="false"
              :loading="getItemPartSelection(itemGroup.id).loading"
              :options="getItemPartSelection(itemGroup.id).options"
              @search="(keyword) => searchItemParts(itemGroup.id, keyword)"
              @focus="() => loadItemParts(itemGroup.id)"
              style="width: 100%"
              show-search
              allow-clear
              :not-found-content="getItemPartSelection(itemGroup.id).loading ? '搜索中...' : '暂无数据'"
              :max-tag-count="3"
              :max-tag-text-length="8"
            >
              <template v-if="getItemPartSelection(itemGroup.id).loading" #suffixIcon>
                <a-spin size="small" />
              </template>
            </a-select>
          </a-form-item>

          <div v-if="getItemPartSelection(itemGroup.id).selectedParts.length > 0" style="margin-top: 8px;">
            <div style="font-size: 12px; color: #666; margin-bottom: 4px;">已选择的部位：</div>
            <a-tag
              v-for="partId in getItemPartSelection(itemGroup.id).selectedParts"
              :key="partId"
              color="green"
              style="margin-bottom: 4px;"
            >
              {{ getBatchPartNameById(itemGroup.id, partId) }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { computed, onMounted, reactive, ref, watchEffect, h } from 'vue';
  import type { Group, Key, Suit } from '#/types';
  import { getItemGroupBySuit, saveItemGroupOfSuit } from '@/views/basicinfo/ItemSuit.api';
  import type { TableColumnType } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import DepartTree from '@/views/basicinfo/components/DepartTree.vue';
  import GroupItemModal from '@/views/basicinfo/components/GroupItemModal.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getAllGroup } from '@/views/basicinfo/ItemGroup.api';
  import { listByItemGroup } from '@/views/basicinfo/CheckPartDict.api';
  import { usePermission } from '@/hooks/web/usePermission';
  import { v4 as uuidv4 } from 'uuid';
  import { debounce } from 'lodash-es';
  import {
    checkItemMutex,
    formatConflictMessage,
    checkItemDependencies,
    formatDependencyMessage,
    getMissingDependencyDetails,
    preloadRelationData,
    checkAllItemsDependencies,
    mergeDependenciesByGroup,
  } from '@/utils/itemGroupRelationManager';
  import { addItemGroupsToSuit } from '@/views/basicinfo/ItemSuit.api';
  import DependencyQuickAddModal from '@/components/DependencyQuickAddModal.vue';

  const { hasPermission } = usePermission();
  const { createErrorModal } = useMessage();

  const groupItemModal = ref();
  const dependencyQuickAddModalRef = ref();

  // 部位选择相关状态
  const checkPartOptions = ref([]);
  const checkPartLoading = ref(false);

  // 依赖项目相关状态
  const missingDependencies = ref([]);
  const addingDependencies = ref(false);
  const lastDependencyCheckTime = ref(0);
  const DEPENDENCY_CHECK_CACHE_DURATION = 30000; // 30秒缓存

  // 批量部位选择相关状态
  const batchPartState = reactive({
    visible: false,
    loading: false,
    itemGroups: [] as Group[], // 需要选择部位的项目列表
    itemPartSelections: new Map<string, {
      options: Array<{label: string, value: string, frequency: number, code: string, name: string}>,
      selectedParts: string[],
      loading: boolean
    }>(), // 每个项目的部位选择状态
    source: '' as 'manual' | 'suit', // 来源：手动添加 或 套餐添加
  });

  interface BatchState {
    disRate: number | null;
    priceAfterDis: number | null;
    type: string;
  }
  const batchFormState = reactive<BatchState>({
    disRate: null,
    priceAfterDis: null,
    type: '健康项目',
  });
  const batchTip = computed(() => {
    if (batchFormState.disRate !== null) {
      return `确定要将所有项目的折扣率设置为${batchFormState.disRate}吗？`;
    } else if (batchFormState.priceAfterDis !== null) {
      return `确定要将所有项目的折后价设置为${batchFormState.priceAfterDis}元吗？`;
    }
    return '确定将所有项目的类型设置为' + batchFormState.type + '吗？';
  });

  function updateByBatch() {
    if (!suitDataSource.value || suitDataSource.value.length == 0) {
      message.warn('没有可以设置的项目！');
      return;
    }
    if (batchFormState.disRate == null && batchFormState.priceAfterDis == null && batchFormState.type == null) {
      message.warn('请设置批量调整条件！');
      return;
    }

    if (batchFormState.disRate !== null) {
      // 更新每一行的折扣率和折后价格
      const inputRate = Number(batchFormState.disRate);
      suitDataSource.value.forEach((row) => {
        // 确保折扣率不低于最小折扣率
        const rate = inputRate > row.minDiscountRate ? inputRate : row.minDiscountRate;
        row.disRate = rate;
        row.priceAfterDis = parseFloat((row.price * rate).toFixed(2));
      });
    } else if (batchFormState.priceAfterDis !== null) {
      //目标价格
      const targetTotalPrice = Number(batchFormState.priceAfterDis);
      //总原价
      const originalTotalPrice = suitDataSource.value.reduce((total, row) => total + row.price, 0);
      //总差价
      const totalDiffAmount = targetTotalPrice - originalTotalPrice;

      if (totalDiffAmount < 0) {
        //可折扣到的最低价格
        const minAvailableTotalPrice = suitDataSource.value.reduce((total, row) => {
          const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
          return total + minPrice;
        }, 0);
        //可以折扣的最多差价
        const totalAvailableDecrease = originalTotalPrice - minAvailableTotalPrice;

        if (-totalDiffAmount > totalAvailableDecrease) {
          createErrorModal({
            title: '操作失败',
            content: '设定的折后总价小于可设置的最小总价 ' + minAvailableTotalPrice.toFixed(2) + ' 元，无法执行该操作！',
          });
          return;
        }

        // 分配减少的金额
        suitDataSource.value.forEach((row) => {
          const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
          const availableDecrease = row.price - minPrice;
          const decreaseAmount = (availableDecrease / totalAvailableDecrease) * -totalDiffAmount;
          row.priceAfterDis = parseFloat((row.price - decreaseAmount).toFixed(2));
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      } else if (totalDiffAmount > 0) {
        // 需要增加价格
        // 暂不考虑价格上限
        suitDataSource.value.forEach((row) => {
          const increaseAmount = (row.price / originalTotalPrice) * totalDiffAmount;
          row.priceAfterDis = parseFloat((row.price + increaseAmount).toFixed(2));
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      } else {
        suitDataSource.value.forEach((row) => {
          row.priceAfterDis = row.price;
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      }

      // 调整因舍入造成的差异
      let finalTotalAmount = suitDataSource.value.reduce((total, row) => total + row.priceAfterDis, 0);
      let finalDiff = parseFloat((targetTotalPrice - finalTotalAmount).toFixed(2));

      if (finalDiff !== 0) {
        suitDataSource.value[0].priceAfterDis = parseFloat((suitDataSource.value[0].priceAfterDis + finalDiff).toFixed(2));
        suitDataSource.value[0].disRate =
          suitDataSource.value[0].price !== 0 ? parseFloat((suitDataSource.value[0].priceAfterDis / suitDataSource.value[0].price).toFixed(2)) : 0;
      }
    }

    // 更新类型
    if (batchFormState.type !== null) {
      suitDataSource.value.forEach((row) => {
        row.type = batchFormState.type;
      });
    }

    updateSuitGroup();
  }

  function showItem(record) {
    groupItemModal.value.showDetail(record.id);
  }

  function updateSuitGroup() {
    let suitGroupList = suitDataSource.value.map((item) => {
      return {
        suitId: props.suitId,
        groupName: item.groupName,
        groupId: item.groupId,
        departmentName: item.departmentName,
        departmentId: item.departmentId,
        price: item.price,
        disRate: item.disRate,
        type: item.type,
        priceAfterDis: item.priceAfterDis,
        minDiscountRate: item.minDiscountRate,
        priceDisDiffAmount: item.priceDisDiffAmount,
        // 添加部位信息
        checkPartId: item.checkPartId || '',
        checkPartName: item.checkPartName || '',
        checkPartCode: item.checkPartCode || '',
        // 添加赠送项目相关字段
        giftBaseId: item.giftBaseId || '',
        isGiftItem: item.isGiftItem || false,
      };
    });

    console.log('Updating suit group with part and gift info:', suitGroupList);
    saveItemGroupOfSuit({ suitId: props.suitId, suitGroupList: suitGroupList });
  }

  function updateMinDiscountRate(record, event) {
    if (event.target.value == record.minDiscountRate) {
      return;
    }
    if (record.minDiscountRate < 0) {
      createErrorModal({ title: '操作失败', content: '最低折扣率不能小于0' });
      fetchData();
      return;
    }
    let rate = Number(event.target.value);
    let disRate = record.disRate || 0;
    if (disRate < rate) {
      createErrorModal({ title: '操作失败', content: '最低折扣率不能大于折扣率' + disRate });
      fetchData();
      return;
    }
    record.minDiscountRate = parseFloat(rate.toFixed(2));
    updateSuitGroup();
  }

  function handlePriceChange(record, event) {
    if (event.target.value == record.priceAfterDis) {
      return;
    }

    let priceAfterDis = Number(event.target.value);
    //判断折后价是否正确
    let rate = record.price != 0 ? priceAfterDis / record.price : 0;
    rate = parseFloat(rate.toFixed(2));
    let minDiscountRate = parseFloat(record.minDiscountRate) || 0;
    if (rate < minDiscountRate) {
      let minPrice = parseFloat((record.price * minDiscountRate).toFixed(2));
      createErrorModal({ title: '操作失败', content: '折后价过低，该项目可设置的最低折后价为' + minPrice + '元！' });
      fetchData();
      return;
    }

    record.priceAfterDis = priceAfterDis;
    record.disRate = rate;
    updateSuitGroup();
  }

  function handleDisRateChange(record, event) {
    if (event.target.value == record.disRate) {
      return;
    }
    let rate = Number(event.target.value);
    let minDiscountRate = record.minDiscountRate || 0;
    if (rate < minDiscountRate) {
      createErrorModal({ title: '操作失败', content: '折扣率不能小于最低折扣率' + minDiscountRate });
      fetchData();
      return;
    }
    record.disRate = parseFloat(rate.toFixed(2));
    record.priceAfterDis = parseFloat((record.price * rate).toFixed(2));
    updateSuitGroup();
  }

  function updateType(record) {
    if (record.type == null) {
      message.error('请选择类型');
      return;
    }
    updateSuitGroup();
  }

  const props = defineProps({
    suitId: String,
  });

  const suitLoading = ref<boolean>(false);
  const suitDataSource = ref<Suit[]>([]);
  const suitTableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: Suit[];
    loading: boolean;
  }>({
    selectedRowKeys: [],
    selectedRows: [],
    loading: false,
  });

  const totalItemPrice = computed(() => suitDataSource.value.reduce((total, row) => total + row.price, 0));
  const totalPriceAfterDis = computed(() => suitDataSource.value.reduce((total, row) => total + row.priceAfterDis, 0));

  const onSuitTableSelectChange = (selectedRowKeys: Key[], selectedRows: Suit[]) => {
    suitTableState.selectedRowKeys = selectedRowKeys;
    suitTableState.selectedRows = selectedRows;
  };

  const suitColumn = [
    {
      title: '组合名称',
      dataIndex: 'groupName',
      ellipsis: false,
      width: '25%',
      customRender: ({ record }) => {
        // 显示赠送项目的特殊标识
        if (record.isGiftItem) {
          return h('span', { style: { color: '#52c41a' } }, record.groupName);
        }
        return record.groupName;
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: '12%',
    },
    {
      title: '检查部位',
      dataIndex: 'checkPartName',
      width: '15%',
    },
    {
      title: '原价',
      dataIndex: 'price',
      width: '8%',
      customRender: ({ record }) => {
        if (record.isGiftItem) {
          return h('span', { style: { color: '#52c41a' } }, '免费');
        }
        return record.price;
      },
    },
    {
      title: '折后价',
      dataIndex: 'priceAfterDis',
      width: '10%',
      customRender: ({ record }) => {
        if (record.isGiftItem) {
          return h('span', { style: { color: '#52c41a' } }, '免费');
        }
        return record.priceAfterDis;
      },
    },
    {
      title: '折扣率',
      dataIndex: 'disRate',
      width: '10%',
    },
    {
      title: '最低折扣率',
      dataIndex: 'minDiscountRate',
      width: '10%',
    }
  ];

  /**左侧项目组合相关操作*/
  let groupListInited = ref(false);
  const selectedDepartId = ref<string | null>(null);
  const groupList = ref<Group[]>([]);
  const groupLoading = ref<boolean>(false);
  const groupDatasource = ref<Group[]>([]);
  const groupTableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: Group[];
    loading: boolean;
    filteredDepart: string | null;
  }>({
    selectedRowKeys: [], // Check here to configure the default column
    selectedRows: [],
    loading: false,
    filteredDepart: null,
  });

  const customRow = (record) => {
    return {
      onClick: () => {
        if (groupTableState.selectedRowKeys.includes(record.id)) {
          groupTableState.selectedRowKeys = groupTableState.selectedRowKeys.filter((key) => key !== record.id);
          groupTableState.selectedRows = groupTableState.selectedRows.filter((row) => row.id !== record.id);
        } else {
          groupTableState.selectedRowKeys.push(record.id);
          groupTableState.selectedRows.push(record);
        }
      },
    };
  };

  const groupColumns = computed<TableColumnType[]>(() => {
    return [
      {
        title: '组合名称',
        dataIndex: 'name',
        width: '40%',
      },
      {
        title: '价格',
        dataIndex: 'price',
        width: '15%',
      },
      {
        title: '科室',
        dataIndex: 'departmentName',
        width: '30%',
        ellipsis: true,
        customFilterDropdown: true,
        onFilter: (value, record) => {
          return record.departmentName == value;
        },
        onFilterDropdownOpenChange: (visible) => {
          if (visible) {
            //openHandle();
          }
        },
      },
      {
        title: '查看',
        dataIndex: 'operation',
        width: '15%',
      },
    ];
  });

  const searchGroupByDepartment = (depart) => {
    if (depart.isLeaf) {
      selectedDepartId.value = depart.id;
      groupDatasource.value = groupList.value.filter((item) => {
        return item.departmentId == depart.id;
      });
    } else {
      selectedDepartId.value = '';
      groupDatasource.value = groupList.value;
    }
  };
  const searchGroupByKeyword = (e) => {
    let keyword = e.target.value;
    if (keyword) {
      groupDatasource.value = groupList.value.filter((item) => {
        let departMatched = true;
        if (selectedDepartId.value) {
          departMatched = item.departmentId == selectedDepartId.value;
        }

        let wordMatched = item.name.includes(keyword) || item.helpChar?.toLowerCase().includes(keyword?.toLowerCase());
        if (wordMatched && departMatched) {
          return true;
        }
        return false;
      });
    } else {
      if (selectedDepartId.value) {
        groupDatasource.value = groupList.value.filter((item) => {
          return item.departmentId == selectedDepartId.value;
        });
      } else {
        groupDatasource.value = groupList.value;
      }
    }
  };

  const onGroupTableSelectChange = (selectedRowKeys: Key[], selectedRows: Group[]) => {
    groupTableState.selectedRowKeys = selectedRowKeys;
    groupTableState.selectedRows = selectedRows;
  };

  const fetchGroup = () => {
    if (!groupListInited.value) {
      groupLoading.value = true;
      getAllGroup({})
        .then((res) => {
          groupList.value = res;
          groupDatasource.value = res;
          groupListInited.value = true;
        })
        .finally(() => {
          groupLoading.value = false;
        });
    }
  };

  async function fetchData() {
    suitLoading.value = true;
    getItemGroupBySuit({ suitId: props.suitId })
      .then((res) => {
        suitDataSource.value = res.suitGroupList;
        console.log('=======================',res.suitGroupList);
        suitDataSource.value.forEach((item) => {
          item.uuid = uuidv4();
        });
      })
      .finally(() => {
        suitLoading.value = false;
      });
  }

  // 添加选中的行到另一个表格
  async function handleAdd() {
    if (groupTableState.selectedRows.length === 0) {
      message.warn('请选择要添加的项目');
      return;
    }

    // 分离需要部位选择的项目和不需要的项目
    const needPartSelectionItems: Group[] = [];
    const addableItems: any[] = [];
    let skippedCount = 0;
    let invalidItems: string[] = [];

    groupTableState.selectedRows.forEach((row) => {
      // 检查项目可用性
      const { isValid, errorMessage } = isItemGroupAvailable(row);
      if (!isValid) {
        invalidItems.push(`${row.name}(${errorMessage})`);
        return;
      }

      // 检查项目是否重复
      const isDuplicate = checkItemGroupDuplicate(suitDataSource.value, row);
      if (isDuplicate) {
        console.log(`项目 ${row.name} 已存在，跳过添加`);
        skippedCount++;
        return;
      }

      // 检查是否需要部位选择
      if (row.hasCheckPart === '1') {
        needPartSelectionItems.push(row);
      } else {
        // 直接添加不需要部位选择的项目
        addableItems.push({
          uuid: uuidv4(),
          groupName: row.name,
          groupId: row.id,
          departmentName: row.departmentName,
          departmentId: row.departmentId,
          type: '健康项目',
          disRate: 1,
          price: row.price,
          priceAfterDis: row.price,
          group: row,
          minDiscountRate: row.minDiscountRate,
          priceDisDiffAmount: 0,
          hasCheckPart: row.hasCheckPart,
          checkPartId: '',
          checkPartName: '',
          checkPartCode: '',
        });
      }
    });

    // 清空选择状态
    groupTableState.selectedRows = [];
    groupTableState.selectedRowKeys = [];

    // 显示错误信息
    if (invalidItems.length > 0) {
      message.warn(`以下项目不符合添加条件：${invalidItems.join('、')}`);
    }

    if (skippedCount > 0) {
      message.warn(`${skippedCount} 个项目已存在，已跳过`);
    }

    // 添加不需要部位选择的项目
    if (addableItems.length > 0) {
      await handleDirectAddItems(addableItems);
    }

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      // 使用批量部位选择器
      showBatchPartSelector(needPartSelectionItems, 'manual');
    }
  }

  // 从表格中移除选中的行
  function handleRemove() {
    //从suitTableState中移除选中的项目
    //suitDataSource.value = suitDataSource.value.filter((row) => !suitTableState.selectedRowKeys.includes(row.groupId));
    suitDataSource.value = suitDataSource.value.filter((row) => !suitTableState.selectedRowKeys.includes(row.uuid));

    updateSuitGroup();
    suitTableState.selectedRowKeys = [];
    suitTableState.selectedRows = [];
  }

  // 批量部位选择辅助方法
  function getItemPartSelection(itemGroupId: string) {
    if (!batchPartState.itemPartSelections.has(itemGroupId)) {
      batchPartState.itemPartSelections.set(itemGroupId, {
        options: [],
        selectedParts: [],
        loading: false
      });
    }
    return batchPartState.itemPartSelections.get(itemGroupId)!;
  }

  function hasAnyPartSelected(): boolean {
    return batchPartState.itemGroups.some(item =>
      getItemPartSelection(item.id).selectedParts.length > 0
    );
  }

  function getBatchPartNameById(itemGroupId: string, partId: string): string {
    const selection = getItemPartSelection(itemGroupId);
    const option = selection.options.find(opt => opt.value === partId);
    return option ? option.name : partId; // 直接返回纯净的部位名称
  }

  // 获取部位选项的完整信息（包含名称、代码等）
  function getBatchPartOptionById(itemGroupId: string, partId: string) {
    const selection = getItemPartSelection(itemGroupId);
    return selection.options.find(opt => opt.value === partId);
  }

  // 显示批量部位选择器
  function showBatchPartSelector(itemGroups: Group[], source: 'manual' | 'suit' = 'manual') {
    // 重置状态
    batchPartState.itemGroups = [...itemGroups];
    batchPartState.source = source;
    batchPartState.itemPartSelections.clear();
    batchPartState.loading = false;

    // 为每个项目初始化选择状态
    itemGroups.forEach(item => {
      getItemPartSelection(item.id);
    });

    // 显示模态框
    batchPartState.visible = true;
  }

  // 关闭批量部位选择器
  function closeBatchPartSelector() {
    batchPartState.visible = false;
    batchPartState.itemGroups = [];
    batchPartState.itemPartSelections.clear();
    batchPartState.source = 'manual';
  }

  // 加载指定项目的部位选项
  async function loadItemParts(itemGroupId: string, keyword?: string) {
    const selection = getItemPartSelection(itemGroupId);
    selection.loading = true;

    try {
      const params = { itemGroupId };
      if (keyword && keyword.trim()) {
        params.keyword = keyword.trim();
      }

      const res = await listByItemGroup(params);

      // 处理直接数组格式
      if (Array.isArray(res)) {
        if (res.length > 0) {
          selection.options = res.map((item: any) => ({
            label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
            value: item.id || '',
            frequency: item.frequency || 0,
            code: item.code || '', // 保存部位代码
            name: item.name || '' // 保存部位名称
          }));
        } else {
          selection.options = [];
          message.warning('没有找到可用的部位选项');
        }
      }
      // 处理包装对象格式
      else if (res && res.success && Array.isArray(res.result)) {
        if (res.result.length > 0) {
          selection.options = res.result.map((item: any) => ({
            label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
            value: item.id || '',
            frequency: item.frequency || 0,
            code: item.code || '', // 保存部位代码
            name: item.name || '' // 保存部位名称
          }));
        } else {
          selection.options = [];
          message.warning('没有找到可用的部位选项');
        }
      } else {
        console.log('⚠ 意外的响应格式:', res);
        selection.options = [];
        message.warning('响应格式异常，无法加载部位选项');
      }
    } catch (error) {
      console.error('加载部位选项失败:', error);
      message.error('加载部位选项失败: ' + error.message);
      selection.options = [];
    } finally {
      selection.loading = false;
    }
  }

  // 搜索指定项目的部位
  const searchItemParts = debounce(async (itemGroupId: string, keyword: string) => {
    if (!keyword || keyword.trim() === '') {
      await loadItemParts(itemGroupId);
    } else {
      await loadItemParts(itemGroupId, keyword.trim());
    }
  }, 300);

  // 批量确认添加项目和部位
  async function confirmBatchAddItemsWithParts() {
    // 验证每个项目都至少选择了一个部位
    const unselectedItems = batchPartState.itemGroups.filter(item =>
      getItemPartSelection(item.id).selectedParts.length === 0
    );

    if (unselectedItems.length > 0) {
      const itemNames = unselectedItems.map(item => item.name).join('、');
      message.warn(`以下项目还未选择部位：${itemNames}`);
      return;
    }

    batchPartState.loading = true;

    try {
      // 准备要添加的项目列表（按照套餐API格式）
      const itemGroups: any[] = [];

      // 为每个项目的每个部位创建记录
      batchPartState.itemGroups.forEach(itemGroup => {
        const selection = getItemPartSelection(itemGroup.id);

        selection.selectedParts.forEach(partId => {
          // 获取部位选项的完整信息
          const partOption = getBatchPartOptionById(itemGroup.id, partId);

          // 直接使用纯净的部位名称和代码，无需额外处理
          const partName = partOption ? partOption.name : '';
          const partCode = partOption ? partOption.code : '';

          // 检查是否已存在（套餐中同一项目的不同部位应该可以添加）
          const exists = suitDataSource.value.some(row =>
            row.groupId === itemGroup.id && row.checkPartId === partId
          );

          if (!exists) {
            // 按照套餐API要求的格式创建项目记录
            const newItem = {
              suitId: props.suitId,
              groupName: itemGroup.name,
              groupId: itemGroup.id, // 套餐API使用groupId而不是itemGroupId
              departmentName: itemGroup.departmentName,
              departmentId: itemGroup.departmentId,
              type: '健康项目',
              disRate: '1', // 字符串格式
              price: itemGroup.price,
              priceAfterDis: itemGroup.price,
              minDiscountRate: itemGroup.minDiscountRate,
              priceDisDiffAmount: 0,
              checkPartId: partId,
              checkPartName: partName,
              checkPartCode: partCode,
            };
            itemGroups.push(newItem);
          }
        });
      });

      if (itemGroups.length === 0) {
        message.warning('所有项目-部位组合都已存在，未添加新项目');
        closeBatchPartSelector();
        return;
      }

      // 调用套餐专用API添加项目（包含附属项目和赠送项目的自动处理）
      const params = {
        suitId: props.suitId,
        itemGroups: itemGroups,
      };

      console.log('调用套餐API添加项目:', params);
      const res = await addItemGroupsToSuit(params);

      if (res.success) {
        message.success(`成功添加 ${itemGroups.length} 个项目-部位组合，后端已自动处理附属项目和赠送项目`);

        // 重新加载套餐数据以获取最新的项目列表（包含附属项目和赠送项目）
        await loadSuitData();

        // 进行依赖检查
        await checkAllDependencies(true);

        closeBatchPartSelector();
      } else {
        message.error(res.message || '添加失败，请重试');
      }

    } catch (error) {
      console.error('批量添加项目失败:', error);
      message.error('添加失败：' + (error.message || '未知错误'));
    } finally {
      batchPartState.loading = false;
    }
  }

  function updateCheckPart(record: any, checkPartId: string) {
    const selectedPart = checkPartOptions.value.find(option => option.value === checkPartId);
    record.checkPartId = checkPartId;
    record.checkPartName = selectedPart ? selectedPart.name : '';
    record.checkPartCode = selectedPart ? selectedPart.code : '';

    console.log('Updated check part:', {
      groupName: record.groupName,
      checkPartId: record.checkPartId,
      checkPartName: record.checkPartName,
      checkPartCode: record.checkPartCode
    });

    // 保存更改
    updateSuitGroup();
  }

  function clearCheckPart(record: any) {
    record.checkPartId = '';
    record.checkPartName = '';
    record.checkPartCode = '';

    console.log('Cleared check part for:', record.groupName);

    // 保存更改
    updateSuitGroup();
  }

  // 检查所有项目的依赖关系
  async function checkAllDependencies(forceCheck = false) {
    console.log('开始检查所有项目依赖关系，项目总数:', suitDataSource.value?.length || 0);

    if (!suitDataSource.value || suitDataSource.value.length === 0) {
      console.log('项目列表为空，清空依赖提示');
      missingDependencies.value = [];
      return;
    }

    // 检查缓存，避免频繁检查
    const now = Date.now();
    if (!forceCheck && now - lastDependencyCheckTime.value < DEPENDENCY_CHECK_CACHE_DURATION) {
      console.log('依赖检查缓存有效，跳过检查');
      return;
    }

    try {
      // 转换套餐项目为依赖检查格式
      const allItems = suitDataSource.value
        .filter(item => item.addMinusFlag !== -1) // 过滤掉减项
        .map(item => ({
          itemGroupId: item.groupId,
          itemGroupName: item.groupName,
          checkPartId: item.checkPartId || '',
          checkPartName: item.checkPartName || '',
        }));

      console.log('转换后的allItems示例:', allItems[0]);
      console.log('过滤前项目数量:', suitDataSource.value.length, '过滤后项目数量:', allItems.length);

      // 使用专门的函数检查所有项目的依赖关系
      const dependencyCheck = await checkAllItemsDependencies(allItems);

      if (!dependencyCheck.isValid && dependencyCheck.missing.length > 0) {
        // 合并相同大项的依赖项目
        const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);

        // 更新缺失依赖项目列表
        missingDependencies.value = mergedDependencies;

        console.log(
          '发现缺失的依赖大项:',
          mergedDependencies.map((dep) => dep.dependentName)
        );
      } else {
        // 如果没有依赖问题或项目为空，清空缺失依赖列表
        console.log('没有依赖问题，清空依赖提示');
        missingDependencies.value = [];
      }

      // 更新检查时间戳
      lastDependencyCheckTime.value = now;
    } catch (error) {
      console.error('检查所有依赖关系失败:', error);
    }
  }

  // 依赖项目UI交互方法
  function openDependencyQuickAddModal() {
    if (missingDependencies.value.length === 0) {
      message.info('当前没有缺失的依赖项目');
      return;
    }
    dependencyQuickAddModalRef.value?.open(missingDependencies.value);
  }

  // 处理依赖项目快捷添加
  async function handleDependencyQuickAdd({ dependencies }) {
    try {
      console.log('开始快捷添加依赖项目:', dependencies);

      // 直接通过API搜索并添加依赖项目
      const projectsToAdd = [];

      for (const dependency of dependencies) {
        try {
          // 通过项目ID直接查询项目信息
          console.log(`搜索依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);

          // 设置搜索条件为项目名称
          const searchParams = {
            name: dependency.dependentName,
            pageNo: 1,
            pageSize: 10,
          };

          // 调用项目搜索API
          const searchResult = await getAllGroup(searchParams);
          console.log(`搜索结果:`, searchResult);

          // getAllGroup返回的是直接的数组，不是分页对象
          const foundProject = Array.isArray(searchResult)
            ? searchResult.find((item) => item.id === dependency.dependentId)
            : null;

          if (foundProject) {
            console.log(`找到依赖项目: ${foundProject.name}`);
            projectsToAdd.push(foundProject);
          } else {
            console.warn(`未找到依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);
          }
        } catch (searchError) {
          console.error(`搜索依赖项目失败: ${dependency.dependentName}`, searchError);
        }
      }

      if (projectsToAdd.length === 0) {
        message.warn('未找到可添加的依赖项目，请检查项目是否存在或手动搜索添加');
        dependencyQuickAddModalRef.value?.setLoading(false);
        return;
      }

      console.log(
        '准备添加的项目:',
        projectsToAdd.map((p) => p.name)
      );

      // 批量添加依赖项目
      await handleAddBatch(projectsToAdd);

      // 关闭模态框
      dependencyQuickAddModalRef.value?.close();

      message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
    } catch (error) {
      console.error('快捷添加依赖项目失败:', error);
      message.error('快捷添加失败: ' + (error.message || '未知错误'));
      dependencyQuickAddModalRef.value?.setLoading(false);
    }
  }

  // 处理依赖检查确认（忽略并继续）
  function handleDependencyConfirm({ dependencies, originalItems, action }) {
    console.log('用户选择忽略依赖检查并继续');
    // 这里可以记录用户的选择，或者执行其他逻辑
  }

  // 处理依赖检查取消
  function handleDependencyCancel({ dependencies, originalItems }) {
    console.log('用户取消了依赖检查');
    // 这里可以执行取消后的清理逻辑
  }

  // 处理一键添加所有依赖项目（从项目列表上方的提示区域）
  async function handleQuickAddAllDependencies() {
    if (missingDependencies.value.length === 0) {
      return;
    }

    try {
      addingDependencies.value = true;

      console.log('开始快捷添加依赖项目:', missingDependencies.value);

      // 直接通过API搜索并添加依赖项目
      const projectsToAdd = [];

      for (const dependency of missingDependencies.value) {
        try {
          // 通过项目ID直接查询项目信息
          console.log(`搜索依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);

          // 设置搜索条件为项目名称
          const searchParams = {
            name: dependency.dependentName,
            pageNo: 1,
            pageSize: 10,
          };

          // 调用项目搜索API
          const searchResult = await getAllGroup(searchParams);
          console.log(`搜索结果:`, searchResult);

          // getAllGroup返回的是直接的数组，不是分页对象
          const foundProject = Array.isArray(searchResult)
            ? searchResult.find((item) => item.id === dependency.dependentId)
            : null;

          if (foundProject) {
            console.log(`找到依赖项目: ${foundProject.name}`);
            projectsToAdd.push(foundProject);
          } else {
            console.warn(`未找到依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);
          }
        } catch (searchError) {
          console.error(`搜索依赖项目失败: ${dependency.dependentName}`, searchError);
        }
      }

      if (projectsToAdd.length === 0) {
        message.warn('未找到可添加的依赖项目，请检查项目是否存在');
        return;
      }

      console.log(
        '准备添加的项目:',
        projectsToAdd.map((p) => p.name)
      );

      // 批量添加依赖项目
      await handleAddBatch(projectsToAdd);

      // 清空缺失依赖列表
      missingDependencies.value = [];

      message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
    } catch (error) {
      console.error('快捷添加依赖项目失败:', error);
      message.error('快捷添加失败: ' + (error.message || '未知错误'));
    } finally {
      addingDependencies.value = false;
    }
  }

  // 批量添加项目（用于依赖项目添加）
  async function handleAddBatch(projects: any[]) {
    if (projects.length === 0) {
      message.warn('请选择要添加的项目');
      return;
    }

    try {
      // 过滤出可添加的项目
      const addableItems: any[] = [];
      const needPartSelectionItems: any[] = [];

      projects.forEach((project) => {
        // 检查项目可用性
        const { isValid, errorMessage } = isItemGroupAvailable(project);
        if (!isValid) {
          console.warn(`项目 ${project.name} 不可用: ${errorMessage}`);
          return;
        }

        // 检查项目是否重复
        const isDuplicate = checkItemGroupDuplicate(suitDataSource.value, project);
        if (isDuplicate) {
          console.log(`项目 ${project.name} 已存在，跳过添加`);
          return;
        }

        // 检查是否需要部位选择
        if (project.hasCheckPart === '1') {
          needPartSelectionItems.push(project);
        } else {
          // 直接添加不需要部位选择的项目
          addableItems.push({
            uuid: uuidv4(),
            groupName: project.name,
            groupId: project.id,
            departmentName: project.departmentName,
            departmentId: project.departmentId,
            type: '健康项目',
            disRate: 1,
            price: project.price,
            priceAfterDis: project.price,
            group: project,
            minDiscountRate: project.minDiscountRate,
            priceDisDiffAmount: 0,
            hasCheckPart: project.hasCheckPart,
            checkPartId: '',
            checkPartName: '',
            checkPartCode: '',
          });
        }
      });

      // 添加不需要部位选择的项目
      if (addableItems.length > 0) {
        await handleDirectAddItems(addableItems);
      }

      // 处理需要部位选择的项目
      if (needPartSelectionItems.length > 0) {
        // 使用批量部位选择器
        showBatchPartSelector(needPartSelectionItems, 'manual');
      }

    } catch (error) {
      console.error('批量添加项目失败:', error);
      message.error('批量添加失败: ' + (error.message || '未知错误'));
    }
  }

  // 项目可用性检查（简化版，套餐中不需要复杂的客户状态检查）
  function isItemGroupAvailable(itemGroup: Group): { isValid: boolean; errorMessage?: string } {
    // 基本的项目状态检查
    if (!itemGroup.id) {
      return { isValid: false, errorMessage: '项目ID无效' };
    }

    if (!itemGroup.name) {
      return { isValid: false, errorMessage: '项目名称无效' };
    }

    // 套餐中的项目基本都是可用的，这里主要做基础验证
    return { isValid: true };
  }

  // 检查项目是否重复 - 套餐版本（简化版）
  function checkItemGroupDuplicate(existingGroups: any[], newGroup: Group): boolean {
    // 仅收费项目允许重复添加
    if (newGroup.chargeItemOnlyFlag === '1') {
      console.log(`项目 ${newGroup.name} 是仅收费项目，允许重复添加`);
      return false;
    }

    // 如果项目不需要部位选择
    if (newGroup.hasCheckPart !== '1') {
      // 检查是否存在相同项目
      const exists = existingGroups.some(item => item.groupId === newGroup.id);
      if (exists) {
        console.log(`项目 ${newGroup.name} 已存在（无需部位选择）`);
      }
      return exists;
    }

    // 如果项目需要部位选择，但没有具体部位信息，不算重复
    if (!newGroup.checkPartId) {
      return false;
    }

    // 有具体部位信息，检查特定部位组合是否重复
    return existingGroups.some(item =>
      item.groupId === newGroup.id && item.checkPartId === newGroup.checkPartId
    );
  }

  // 处理直接添加项目（不需要部位选择）
  async function handleDirectAddItems(addableItems: any[]) {
    try {
      // 准备要添加的项目列表（按照套餐API格式）
      const itemGroups = addableItems.map(item => ({
        suitId: props.suitId,
        groupName: item.groupName,
        groupId: item.groupId, // 套餐API使用groupId
        departmentName: item.departmentName,
        departmentId: item.departmentId,
        type: item.type,
        disRate: String(item.disRate), // 转换为字符串
        price: item.price,
        priceAfterDis: item.priceAfterDis,
        minDiscountRate: item.minDiscountRate,
        priceDisDiffAmount: item.priceDisDiffAmount,
        checkPartId: item.checkPartId,
        checkPartName: item.checkPartName,
        checkPartCode: item.checkPartCode,
      }));

      // 调用套餐专用API添加项目（包含附属项目和赠送项目的自动处理）
      const params = {
        suitId: props.suitId,
        itemGroups: itemGroups,
      };

      console.log('调用套餐API直接添加项目:', params);
      const res = await addItemGroupsToSuit(params);

      if (res.success) {
        message.success(`成功添加 ${itemGroups.length} 个项目，后端已自动处理附属项目和赠送项目`);

        // 重新加载套餐数据以获取最新的项目列表（包含附属项目和赠送项目）
        await loadSuitData();

        // 进行依赖检查
        await checkAllDependencies(true);
      } else {
        message.error(res.message || '添加失败，请重试');
      }

    } catch (error) {
      console.error('直接添加项目失败:', error);
      message.error('添加失败：' + (error.message || '未知错误'));
    }
  }

  // 重新加载套餐数据
  async function loadSuitData() {
    try {
      suitLoading.value = true;
      const res = await getItemGroupBySuit({ suitId: props.suitId });

      // 使用正确的数据格式：res.suitGroupList
      suitDataSource.value = res.suitGroupList || [];

      // 添加uuid字段
      suitDataSource.value.forEach((item: any) => {
        if (!item.uuid) {
          item.uuid = uuidv4();
        }
      });

      console.log('重新加载套餐数据完成，项目数量:', suitDataSource.value.length);
      console.log('套餐数据详情:', suitDataSource.value);
    } catch (error) {
      console.error('加载套餐数据失败:', error);
      message.error('加载套餐数据失败');
    } finally {
      suitLoading.value = false;
    }
  }

  watchEffect(async () => {
    if (props.suitId) {
      await fetchData();
      // 加载完成后进行依赖检查
      await checkAllDependencies(true);
    } else {
      suitDataSource.value = [];
      missingDependencies.value = [];
    }
  });

  onMounted(() => {
    fetchGroup();
  });
  defineExpose({
    fetchData,
  });
</script>
