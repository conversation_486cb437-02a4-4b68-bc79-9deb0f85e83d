<template>
  <div>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <!--主表区域 -->
      <a-tab-pane tab="报告分组设置" :key="refKeys[0]" :forceRender="true" :style="tabsStyle">
        <BasicForm @register="registerForm" ref="formRef" />
      </a-tab-pane>
      <!--子表单区域 -->
      <a-tab-pane tab="报告分组设置-关联科室" key="reportSettingDepart" :forceRender="true" :style="tabsStyle">
        <div class="mb-2">
          <a-button type="dashed" @click="addDepart" :disabled="formDisabled">新增科室</a-button>
        </div>
        <a-table
          :data-source="reportSettingDepartTable.dataSource"
          :columns="departColumns"
          :pagination="false"
          :rowKey="row => row.id || row._key"
          :loading="reportSettingDepartTable.loading"
          :scroll="{ y: 340, x: 800 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'departmentId'">
              <span>{{ renderDictText('sys_depart,depart_name,id', record.departmentId) }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'seq'">
              <span>{{ record.seq }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="editDepart(record, index)" :disabled="formDisabled">编辑</a-button>
                <a-button type="link" danger @click="removeDepart(record, index)" :disabled="formDisabled">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <BasicModal @register="registerDepartModal" :title="departModalTitle" :width="640" @ok="handleDepartOk">
          <BasicForm @register="registerDepartForm" />
        </BasicModal>
      </a-tab-pane>
      <a-tab-pane tab="报告分组设置-关联大项" key="reportSettingItemgroup" :forceRender="true" :style="tabsStyle">
        <div class="mb-2">
          <a-button type="dashed" @click="addItemgroup" :disabled="formDisabled">新增大项</a-button>
        </div>
        <a-table
          :data-source="reportSettingItemgroupTable.dataSource"
          :columns="itemgroupColumns"
          :pagination="false"
          :rowKey="row => row.id || row._key"
          :loading="reportSettingItemgroupTable.loading"
          :scroll="{ y: 340, x: 800 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'itemgroupId'">
              <span>{{ renderDictText('item_group where del_flag=0 and enable_flag=1,name,id', record.itemgroupId) }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'seq'">
              <span>{{ record.seq }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="editItemgroup(record, index)" :disabled="formDisabled">编辑</a-button>
                <a-button type="link" danger @click="removeItemgroup(record, index)" :disabled="formDisabled">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <BasicModal @register="registerItemgroupModal" :title="itemgroupModalTitle" :width="640" @ok="handleItemgroupOk">
          <BasicForm @register="registerItemgroupForm" />
        </BasicModal>
      </a-tab-pane>
    </a-tabs>

    <div style="width: 100%; text-align: center; margin-top: 10px" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defHttp } from '/@/utils/http/axios';
  import { ref, computed, unref, reactive, onMounted, defineProps } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { formSchema } from '../ReportSettingGroup.data';
  import { saveOrUpdate, reportSettingDepartList, reportSettingItemgroupList } from '../ReportSettingGroup.api';
  import { getDictItemsByCode } from '/@/utils/dict/index';
  const refKeys = ref(['reportSettingGroup', 'reportSettingDepart', 'reportSettingItemgroup']);
  const activeKey = ref('reportSettingGroup');
  const reportSettingDepart = ref();
  const reportSettingItemgroup = ref();
  const reportSettingDepartTable = reactive({
    loading: false,
    dataSource: [],
    show: false,
  });
  const reportSettingItemgroupTable = reactive({
    loading: false,
    dataSource: [],
    show: false,
  });

  // 表格列配置
  const departColumns = [
    { title: '科室', dataIndex: 'departmentId', width: 300 },
    { title: '排序', dataIndex: 'seq', width: 200 },
    { title: '操作', key: 'action', width: 150, fixed: 'right' },
  ];
  const itemgroupColumns = [
    { title: '大项', dataIndex: 'itemgroupId', width: 300 },
    { title: '排序号', dataIndex: 'seq', width: 200 },
    { title: '操作', key: 'action', width: 150, fixed: 'right' },
  ];

  // 字典渲染函数
  function renderDictText(dictCode: string, value: any) {
    const arr = getDictItemsByCode(dictCode) || [];
    const hit = arr.find((it) => String(it.value) === String(value));
    return hit ? hit.text : value;
  }

  const props = defineProps({
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const formDisabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      }
    }
    return true;
  });
  // 是否显示提交按钮
  const showFlowSubmitButton = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return true;
      }
    }
    return false;
  });

  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  onMounted(() => {
    initFormData();
  });
  //渲染流程表单数据
  const queryByIdUrl = '/basicinfo/reportSettingGroup/queryById';
  async function initFormData() {
    if (props.formBpm === true) {
      await reset();
      let params = { id: props.formData.dataId };
      const data = await defHttp.get({ url: queryByIdUrl, params });
      //表单赋值
      await setFieldsValue({
        ...data,
      });
      requestSubTableData(reportSettingDepartList, { id: data.id }, reportSettingDepartTable, () => {
        reportSettingDepartTable.show = true;
      });
      requestSubTableData(reportSettingItemgroupList, { id: data.id }, reportSettingItemgroupTable, () => {
        reportSettingItemgroupTable.show = true;
      });
      // 隐藏底部时禁用整个表单
      setProps({ disabled: formDisabled.value });
    }
  }

  //方法配置
  const formRef = ref();
  function handleChangeTabs() {}

  async function requestSubTableData(api, params, table, done) {
    table.loading = true;
    try {
      const res = await defHttp.get({ url: api, params });
      table.dataSource = (res?.result || []).map((row, idx) => ({
        id: row.id,
        departmentId: row.departmentId,
        itemgroupId: row.itemgroupId,
        seq: row.seq,
        _key: row.id || `temp_${idx}`,
      }));
      done && done();
    } finally {
      table.loading = false;
    }
  }
  // 弹窗tabs滚动区域的高度
  const tabsStyle = computed(() => {
    let height: Nullable<string> = null;
    let minHeight = '100px';
    // 弹窗wrapper
    let overflow = 'auto';
    return { height, minHeight, overflow };
  });

  async function reset() {
    await resetFields();
    activeKey.value = 'reportSettingGroup';
    reportSettingDepartTable.dataSource = [];
    reportSettingItemgroupTable.dataSource = [];
  }
  function classifyIntoFormData(formValue) {
    return {
      ...formValue,
      reportSettingDepartList: reportSettingDepartTable.dataSource.map((row, idx) => ({
        id: row.id,
        departmentId: row.departmentId,
        seq: row.seq ?? idx + 1,
      })),
      reportSettingItemgroupList: reportSettingItemgroupTable.dataSource.map((row, idx) => ({
        id: row.id,
        itemgroupId: row.itemgroupId,
        seq: row.seq ?? idx + 1,
      })),
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    //提交表单
    await saveOrUpdate(values, true);
  }

  // 科室相关操作
  const departModalTitle = ref('编辑科室');
  const [registerDepartModal, { openModal: openDepartModal, closeModal: closeDepartModal }] = useModal();
  const [registerDepartForm, { setFieldsValue: setDepartFieldsValue, resetFields: resetDepartFields, validate: validateDepartForm }] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: [
      { label: '科室', field: 'departmentId', component: 'JDictSelectTag', componentProps: { dictCode: 'sys_depart,depart_name,id' }, dynamicRules: () => [{ required: true, message: '请选择科室!' }] },
      { label: '排序', field: 'seq', component: 'InputNumber', dynamicRules: () => [{ required: true, message: '请输入排序!' }] },
    ],
  });

  let editingDepartIndex = -1;
  function addDepart() {
    editingDepartIndex = -1;
    resetDepartFields();
    departModalTitle.value = '新增科室';
    openDepartModal(true, {});
  }
  function editDepart(record, index) {
    editingDepartIndex = index;
    resetDepartFields();
    setDepartFieldsValue({ ...record });
    departModalTitle.value = '编辑科室';
    openDepartModal(true, {});
  }
  function removeDepart(_row, index) {
    reportSettingDepartTable.dataSource.splice(index, 1);
  }
  async function handleDepartOk() {
    const values = await validateDepartForm();
    if (editingDepartIndex >= 0) {
      reportSettingDepartTable.dataSource.splice(editingDepartIndex, 1, { ...reportSettingDepartTable.dataSource[editingDepartIndex], ...values });
    } else {
      reportSettingDepartTable.dataSource.push({ ...values, _key: `temp_${Date.now()}` });
    }
    closeDepartModal();
  }

  // 大项相关操作
  const itemgroupModalTitle = ref('编辑大项');
  const [registerItemgroupModal, { openModal: openItemgroupModal, closeModal: closeItemgroupModal }] = useModal();
  const [registerItemgroupForm, { setFieldsValue: setItemgroupFieldsValue, resetFields: resetItemgroupFields, validate: validateItemgroupForm }] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: [
      { label: '大项', field: 'itemgroupId', component: 'JSearchSelect', componentProps: { dict: 'item_group where del_flag=0 and enable_flag=1,name,id' }, dynamicRules: () => [{ required: true, message: '请选择大项!' }] },
      { label: '排序号', field: 'seq', component: 'InputNumber', dynamicRules: () => [{ required: true, message: '请输入排序号!' }] },
    ],
  });

  let editingItemgroupIndex = -1;
  function addItemgroup() {
    editingItemgroupIndex = -1;
    resetItemgroupFields();
    itemgroupModalTitle.value = '新增大项';
    openItemgroupModal(true, {});
  }
  function editItemgroup(record, index) {
    editingItemgroupIndex = index;
    resetItemgroupFields();
    setItemgroupFieldsValue({ ...record });
    itemgroupModalTitle.value = '编辑大项';
    openItemgroupModal(true, {});
  }
  function removeItemgroup(_row, index) {
    reportSettingItemgroupTable.dataSource.splice(index, 1);
  }
  async function handleItemgroupOk() {
    const values = await validateItemgroupForm();
    if (editingItemgroupIndex >= 0) {
      reportSettingItemgroupTable.dataSource.splice(editingItemgroupIndex, 1, { ...reportSettingItemgroupTable.dataSource[editingItemgroupIndex], ...values });
    } else {
      reportSettingItemgroupTable.dataSource.push({ ...values, _key: `temp_${Date.now()}` });
    }
    closeItemgroupModal();
  }

  // 主表提交
  async function handleSubmit() {
    const formValue = await validate();
    const payload = classifyIntoFormData(formValue);
    await requestAddOrEdit(payload);
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .ant-modal-header {
      padding-top: 8px;
      padding-bottom: 8px;
      border-bottom: none !important;
    }

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
