<template>
  <a-modal
    title="复查管理"
    width="80%"
    :open="visible"
    @ok="handleCancel"
    :okButtonProps="{ class: { 'jee-hidden': true } }"
    @cancel="handleCancel"
    cancelText="关闭"
  >
    <div style="height: 75vh; overflow-y: auto">
      <a-row :gutter="8">
        <a-col :span="10">
          <a-card size="small" title="1、请选择复查项目">
            <template #extra> </template>
            <a-row :gutter="2">
              <a-col :span="8" style="height: 60vh; overflow: auto">
                <DepartTree ref="leftTree" @select="searchGroupByDepartment" @root-tree-data="onRootTreeData" />
              </a-col>
              <a-col :span="16">
                <a-form layout="inline" :model="searchForm" style="margin-bottom: 10px">
                  <a-form-item labelAlign="left">
                    <a-input-search allow-clear @change="searchGroupByKeyword" size="middle" placeholder="名称或助记码查询" />
                  </a-form-item>
                  <a-button type="primary" @click="handelSelected">批量添加<ArrowRightOutlined /></a-button>
                </a-form>
                <a-table
                  :loading="groupLoading"
                  :bordered="false"
                  :scroll="{ y: '60vh' }"
                  :pagination="false"
                  row-key="id"
                  :columns="groupColumns"
                  :data-source="groupDatasource"
                  size="small"
                  :row-selection="{ selectedRowKeys: groupTableState.selectedRowKeys, onChange: onGroupTableSelectChange }"
                >
                  <template #bodyCell="{ text, record, index, column }">
                    <template v-if="column.dataIndex === 'operation'">
                      <!--                      <a @click="showItem(record)">项目</a>
                        <a-divider type="vertical" />-->
                      <a @click="handleAddOne(record)">添加</a>
                    </template>
                  </template>
                </a-table>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
        <a-col :span="14">
          <a-card size="small" title="2、填写复查信息">
            <template #extra>
              <a-button type="primary" @click="handleSave"><SaveOutlined />保存</a-button>
            </template>
            <RecheckNotifyForm
              ref="registerForm"
              @ok="submitCallback"
              :formDisabled="disableSubmit"
              :formBpm="false"
              :customer-reg-id="customerRegId"
              :summary-id="summaryId"
            />
          </a-card>
          <a-card size="small" title="复查列表" style="margin-top: 8px">
            <RecheckNotifyList ref="registerList" @update-total="handleUpdateTotal" :customer-reg-id="customerRegId" :summary-id="summaryId" />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, nextTick, reactive, ref } from 'vue';
  import RecheckNotifyForm from '@/views/recheck/components/RecheckNotifyForm.vue';
  import RecheckNotifyList from '@/views/recheck/RecheckNotifyList.vue';
  import DepartTree from '@/views/basicinfo/components/DepartTree.vue';
  import { ArrowRightOutlined, SaveOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { Group, Key } from '#/types';
  import type { TableColumnType } from 'ant-design-vue';
  import { getAllGroup } from '@/views/basicinfo/ItemGroup.api';

  const registerForm = ref();
  const registerList = ref();

  const width = ref<string>('80%');
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const emit = defineEmits(['register', 'success', 'update-total']);
  const { createMessage } = useMessage();
  const customerRegId = ref();
  const summaryId = ref();

  /**项目组合列表*/
  const rootTreeData = ref<any[]>([]);
  const groupList = ref();
  const groupListInited = ref<boolean>(false);
  // 左侧树选择后触发

  // 左侧树rootTreeData触发
  function onRootTreeData(data) {
    rootTreeData.value = data;
  }

  const selectedDepartId = ref<string | null>(null);
  const searchForm = reactive({});
  const groupLoading = ref<boolean>(false);
  const groupDatasource = ref<Group[]>([]);
  const groupTableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: Group[];
    loading: boolean;
    filteredDepart: string | null;
  }>({
    selectedRowKeys: [], // Check here to configure the default column
    selectedRows: [],
    loading: false,
    filteredDepart: null,
  });

  const groupColumns = computed<TableColumnType[]>(() => {
    return [
      {
        title: '组合名称',
        dataIndex: 'name',
        width: '40%',
      },
      {
        title: '科室',
        dataIndex: 'departmentName',
        width: '30%',
        ellipsis: true,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: '15%',
      },
    ];
  });

  const fetchGroup = () => {
    if (!groupListInited.value) {
      groupLoading.value = true;
      getAllGroup({})
        .then((res) => {
          groupList.value = res;
          groupDatasource.value = res;
          groupListInited.value = true;
        })
        .finally(() => {
          groupLoading.value = false;
        });
    }
  };
  const searchGroupByDepartment = (depart) => {
    if (depart.isLeaf) {
      selectedDepartId.value = depart.id;
      groupDatasource.value = groupList.value.filter((item) => {
        return item.departmentId == depart.id;
      });
    } else {
      selectedDepartId.value = '';
      groupDatasource.value = groupList.value;
    }
  };
  const searchGroupByKeyword = (e) => {
    let keyword = e.target.value;
    if (keyword) {
      groupDatasource.value = groupList.value.filter((item) => {
        let departMatched = true;
        if (selectedDepartId.value) {
          departMatched = item.departmentId == selectedDepartId.value;
        }

        let wordMatched = item.name.includes(keyword) || item.helpChar?.toLowerCase().includes(keyword?.toLowerCase());
        if (wordMatched && departMatched) {
          return true;
        }
       return false;
      });
    } else {
      if (selectedDepartId.value) {
        groupDatasource.value = groupList.value.filter((item) => {
          return item.departmentId == selectedDepartId.value;
        });
      } else {
        groupDatasource.value = groupList.value;
      }
    }
  };
  const onGroupTableSelectChange = (selectedRowKeys: Key[], selectedRows: Group[]) => {
    groupTableState.selectedRowKeys = selectedRowKeys;
    groupTableState.selectedRows = selectedRows;
  };

  function handelSelected() {
    if (groupTableState.selectedRows.length == 0) {
      createMessage.error('请选择复查项目');
    }

    let selectedItemGroups = groupTableState.selectedRows.map((item) => {
      return {
        name: item.name,
        id: item.id,
      };
    });
    registerForm.value.add(selectedItemGroups);
  }
  function handleAddOne(record) {
    registerForm.value.add([{ name: record.name, id: record.id }]);
  }

  function handleUpdateTotal(total) {
    emit('update-total', total);
  }

  /**
   * 打开
   */
  function open(customerRegIdParam, summaryIdParam) {
    customerRegId.value = customerRegIdParam;
    summaryId.value = summaryIdParam;
    visible.value = true;
    nextTick(() => {
      fetchGroup();
    });
  }

  /**
   * 确定按钮点击事件
   */
  function handleSave() {
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    registerList.value?.reloadPage();
    emit('success');
  }

  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    open,
    disableSubmit,
  });
</script>

<style>
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
