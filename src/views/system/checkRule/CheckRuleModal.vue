<template>
  <BasicModal v-bind="$attrs" @register="registerModal" @ok="handleSubmit" :title="title" :width="1200" destroyOnClose>
    <BasicForm @register="registerForm" />

    <a-tabs v-model:activeKey="activeKey" animated>
      <a-tab-pane tab="局部规则" key="1" :forceRender="true">
        <a-alert type="info" showIcon message="局部规则按照你输入的位数有序的校验" style="margin-bottom: 8px" />
        <div class="mb-2">
          <a-button type="dashed" @click="addLocalRule">新增局部规则</a-button>
        </div>
        <a-table
          :data-source="dataSource1"
          :columns="localRuleColumns"
          :pagination="false"
          :rowKey="row => row.id || row._key"
          :scroll="{ y: 480, x: 800 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'digits'">
              <span>{{ record.digits }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'pattern'">
              <span>{{ record.pattern }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'message'">
              <span>{{ record.message }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="editLocalRule(record, index)">编辑</a-button>
                <a-button type="link" danger @click="removeLocalRule(record, index)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <BasicModal @register="registerLocalModal" :title="localModalTitle" :width="640" @ok="handleLocalOk">
          <BasicForm @register="registerLocalForm" />
        </BasicModal>
      </a-tab-pane>
      <a-tab-pane tab="全局规则" key="2" :forceRender="true">
        <a-alert type="info" showIcon message="全局规则可校验用户输入的所有字符；全局规则的优先级比局部规则的要高。" style="margin-bottom: 8px" />
        <div class="mb-2">
          <a-button type="dashed" @click="addGlobalRule">新增全局规则</a-button>
        </div>
        <a-table
          :data-source="dataSource2"
          :columns="globalRuleColumns"
          :pagination="false"
          :rowKey="row => row.id || row._key"
          :scroll="{ y: 480, x: 800 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'priority'">
              <span>{{ record.priority === '1' ? '优先运行' : '最后运行' }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'pattern'">
              <span>{{ record.pattern }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'message'">
              <span>{{ record.message }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="editGlobalRule(record, index)">编辑</a-button>
                <a-button type="link" danger @click="removeGlobalRule(record, index)">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <BasicModal @register="registerGlobalModal" :title="globalModalTitle" :width="640" @ok="handleGlobalOk">
          <BasicForm @register="registerGlobalForm" />
        </BasicModal>
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { computed, ref, unref } from 'vue';
  import { formSchema } from './check.rule.data';
  import { saveCheckRule, updateCheckRule } from './check.rule.api';
  import { pick } from 'lodash-es';

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);

  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  const activeKey = ref('1');
  let arr1: any[] = [];
  let dataSource1 = ref(arr1);
  let arr2: any[] = [];
  let dataSource2 = ref(arr2);

  // 表格列配置
  const localRuleColumns = [
    { title: '位数', dataIndex: 'digits', width: 120 },
    { title: '规则（正则表达式）', dataIndex: 'pattern', width: 300 },
    { title: '提示文本', dataIndex: 'message', width: 200 },
    { title: '操作', key: 'action', width: 150, fixed: 'right' },
  ];

  const globalRuleColumns = [
    { title: '优先级', dataIndex: 'priority', width: 120 },
    { title: '规则（正则表达式）', dataIndex: 'pattern', width: 300 },
    { title: '提示文本', dataIndex: 'message', width: 200 },
    { title: '操作', key: 'action', width: 150, fixed: 'right' },
  ];

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    activeKey.value = '1';
    dataSource1.value = [];
    dataSource2.value = [];
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });

      let ruleJson = data.record.ruleJson;
      if (ruleJson) {
        let ruleList = JSON.parse(ruleJson);
        // 筛选出全局规则和局部规则
        let global: any[] = [],
          design: any[] = [],
          priority = '1';
        ruleList.forEach((rule) => {
          if (rule.digits === '*') {
            global.push(Object.assign(rule, { priority }));
          } else {
            priority = '0';
            design.push(rule);
          }
        });
        dataSource1.value = design.map((item, idx) => ({ ...item, _key: item.id || `local_${idx}` }));
        dataSource2.value = global.map((item, idx) => ({ ...item, _key: item.id || `global_${idx}` }));
      }
    }
  });

  // 验证表格数据
  function validateLocalTable() {
    return new Promise((resolve, reject) => {
      // 简单验证：检查必填字段
      const hasError = dataSource1.value.some(item => !item.digits || !item.pattern || !item.message);
      if (hasError) {
        activeKey.value = '1';
        reject('局部规则存在必填字段为空的记录');
      } else {
        resolve(dataSource1.value);
      }
    });
  }

  function validateGlobalTable() {
    return new Promise((resolve, reject) => {
      // 简单验证：检查必填字段
      const hasError = dataSource2.value.some(item => !item.priority || !item.pattern || !item.message);
      if (hasError) {
        activeKey.value = '2';
        reject('全局规则存在必填字段为空的记录');
      } else {
        resolve(dataSource2.value);
      }
    });
  }

  //表单提交事件
  async function handleSubmit() {
    let mainData;
    let globalValues = [];
    let designValues = [];
    validate()
      .then((formValue) => {
        mainData = formValue;
        return validateLocalTable();
      })
      .then((tableData1: []) => {
        if (tableData1 && tableData1.length > 0) {
          designValues = tableData1;
        }
        return validateGlobalTable();
      })
      .then((tableData2: []) => {
        if (tableData2 && tableData2.length > 0) {
          globalValues = tableData2;
        }
        // 整合两个子表的数据
        let firstGlobal: any[] = [],
          afterGlobal: any[] = [];
        for (let i = 0; i < globalValues.length; i++) {
          let v: any = globalValues[i];
          v.digits = '*';
          if (v.priority === '1') {
            firstGlobal.push(v);
          } else {
            afterGlobal.push(v);
          }
        }
        let concatValues = firstGlobal.concat(designValues).concat(afterGlobal);
        let subValues = concatValues.map((i) => pick(i, 'digits', 'pattern', 'message'));
        // 生成 formData，用于传入后台
        let ruleJson = JSON.stringify(subValues);
        let formData = Object.assign({}, mainData, { ruleJson });
        saveOrUpdateFormData(formData);
      })
      .catch((error) => {
        setModalProps({ confirmLoading: false });
        console.error('验证未通过!', error);
      });
  }

  // 表单提交请求
  async function saveOrUpdateFormData(formData) {
    try {
      console.log('表单提交数据', formData);
      setModalProps({ confirmLoading: true });
      if (isUpdate.value) {
        await updateCheckRule(formData);
      } else {
        await saveCheckRule(formData);
      }
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  /**
   * 校验正则表达式
   */
  const validatePattern = (_, value) => {
    return new Promise((resolve, reject) => {
      if (!value) {
        reject('规则不能为空');
        return;
      }
      try {
        new RegExp(value);
        resolve();
      } catch (e) {
        reject('请输入正确的正则表达式');
      }
    });
  };

  // 局部规则相关操作
  const localModalTitle = ref('编辑局部规则');
  const [registerLocalModal, { openModal: openLocalModal, closeModal: closeLocalModal }] = useModal();
  const [registerLocalForm, { setFieldsValue: setLocalFieldsValue, resetFields: resetLocalFields, validate: validateLocalForm }] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: [
      {
        label: '位数',
        field: 'digits',
        component: 'InputNumber',
        componentProps: { min: 1 },
        dynamicRules: () => [
          { required: true, message: '位数不能为空' },
          { pattern: /^[1-9]\d*$/, message: '请输入零以上的正整数' }
        ]
      },
      {
        label: '规则（正则表达式）',
        field: 'pattern',
        component: 'Input',
        dynamicRules: () => [
          { required: true, message: '规则不能为空' },
          { validator: validatePattern }
        ]
      },
      {
        label: '提示文本',
        field: 'message',
        component: 'Input',
        dynamicRules: () => [{ required: true, message: '提示文本不能为空' }]
      },
    ],
  });

  let editingLocalIndex = -1;
  function addLocalRule() {
    editingLocalIndex = -1;
    resetLocalFields();
    localModalTitle.value = '新增局部规则';
    openLocalModal(true, {});
  }
  function editLocalRule(record, index) {
    editingLocalIndex = index;
    resetLocalFields();
    setLocalFieldsValue({ ...record });
    localModalTitle.value = '编辑局部规则';
    openLocalModal(true, {});
  }
  function removeLocalRule(_row, index) {
    dataSource1.value.splice(index, 1);
  }
  async function handleLocalOk() {
    const values = await validateLocalForm();
    if (editingLocalIndex >= 0) {
      dataSource1.value.splice(editingLocalIndex, 1, { ...dataSource1.value[editingLocalIndex], ...values });
    } else {
      dataSource1.value.push({ ...values, _key: `local_${Date.now()}` });
    }
    closeLocalModal();
  }

  // 全局规则相关操作
  const globalModalTitle = ref('编辑全局规则');
  const [registerGlobalModal, { openModal: openGlobalModal, closeModal: closeGlobalModal }] = useModal();
  const [registerGlobalForm, { setFieldsValue: setGlobalFieldsValue, resetFields: resetGlobalFields, validate: validateGlobalForm }] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: [
      {
        label: '优先级',
        field: 'priority',
        component: 'Select',
        componentProps: {
          options: [
            { label: '优先运行', value: '1' },
            { label: '最后运行', value: '0' },
          ]
        },
        dynamicRules: () => [{ required: true, message: '请选择优先级' }]
      },
      {
        label: '规则（正则表达式）',
        field: 'pattern',
        component: 'Input',
        dynamicRules: () => [
          { required: true, message: '规则不能为空' },
          { validator: validatePattern }
        ]
      },
      {
        label: '提示文本',
        field: 'message',
        component: 'Input',
        dynamicRules: () => [{ required: true, message: '提示文本不能为空' }]
      },
    ],
  });

  let editingGlobalIndex = -1;
  function addGlobalRule() {
    editingGlobalIndex = -1;
    resetGlobalFields();
    globalModalTitle.value = '新增全局规则';
    openGlobalModal(true, {});
  }
  function editGlobalRule(record, index) {
    editingGlobalIndex = index;
    resetGlobalFields();
    setGlobalFieldsValue({ ...record });
    globalModalTitle.value = '编辑全局规则';
    openGlobalModal(true, {});
  }
  function removeGlobalRule(_row, index) {
    dataSource2.value.splice(index, 1);
  }
  async function handleGlobalOk() {
    const values = await validateGlobalForm();
    if (editingGlobalIndex >= 0) {
      dataSource2.value.splice(editingGlobalIndex, 1, { ...dataSource2.value[editingGlobalIndex], ...values });
    } else {
      dataSource2.value.push({ ...values, _key: `global_${Date.now()}` });
    }
    closeGlobalModal();
  }


</script>
