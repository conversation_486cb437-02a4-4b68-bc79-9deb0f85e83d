import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import viteCompression from 'vite-plugin-compression'; // 引入 Gzip 压缩插件

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv;

  const isBuild = command === 'build';

  const serverOptions: Recordable = {};

  // ----- [begin] 【JEECG作为乾坤子应用】 -----
  const isQiankunMicro = false;

  // ----- [end] 【JEECG作为乾坤子应用】 -----

  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      // Listening on all local IPs
      host: true,
      // @ts-ignore
      https: false,
      port: VITE_PORT,
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY),
      // 合并 server 配置
      ...serverOptions,
    },
    build: {
      minify: 'esbuild',
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      rollupOptions: {
        // 关闭除屑优化，防止删除重要代码，导致打包后功能出现异常
        treeshake: false,
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          // manualChunks配置 (依赖包从大到小排列) - 优化代码分割
          manualChunks: {
            // 核心框架 - 最高优先级，会被最先加载
            'vue-vendor': ['vue', 'vue-router'],
            // UI组件库 - 按使用频率分割
            'antd-vue-vendor': ['ant-design-vue', '@ant-design/icons-vue', '@ant-design/colors'],
            // 工具库 - 按功能分割
            'utils-vendor': ['lodash-es', 'dayjs', 'axios'],
            // 编辑器和富文本
            'editor-vendor': ['@tinymce/tinymce-vue'],
            // 特殊功能组件
            'emoji-mart-vue-fast': ['emoji-mart-vue-fast'],
            'china-area-data-vendor': ['china-area-data'],
            // 图表和可视化
            'charts-vendor': ['echarts'],
          },
        },
      },
      // 关闭brotliSize显示可以稍微减少打包时间
      reportCompressedSize: false,
      // 提高超大静态资源警告大小
      chunkSizeWarningLimit: 2000,
    },
    esbuild: {
      //清除全局的console.log和debug
      drop: isBuild ? ['console', 'debugger'] : [],
      // 增加 esbuild 稳定性配置
      keepNames: true,
      target: 'es2020',
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    // 预加载构建配置（首屏性能)
    plugins: [
      // 调用原有的插件生成函数
      ...createVitePlugins(viteEnv, isBuild, isQiankunMicro),
      // 新增 Gzip 压缩插件
      viteCompression({
        algorithm: 'gzip', // 使用 Gzip 压缩
        ext: '.gz', // 生成 .gz 文件
        threshold: 10240, // 对大于 10KB 的文件进行压缩
        deleteOriginFile: false, // 是否删除原始文件
      }),
    ],
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
        // 增加 esbuild 稳定性配置
        keepNames: true,
        // 设置更大的内存限制
        define: {
          global: 'globalThis',
        },
      },
      // 🚀 性能优化：预构建关键依赖，减少网络请求
      include: [
        'clipboard',
        'vue',
        'vue-router',
        'ant-design-vue',
        '@ant-design/icons-vue',
        'lodash-es',
        'dayjs',
        'axios',
      ],
      exclude: [
        //升级vite4后，需要排除online依赖
        '@jeecg/online',
      ],
      // 强制重新构建依赖
      force: true,
    },
  };
};
